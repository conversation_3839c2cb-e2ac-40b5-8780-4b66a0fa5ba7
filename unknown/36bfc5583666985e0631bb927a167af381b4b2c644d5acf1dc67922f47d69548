.editor-container {
  display: flex;
  height: 80vh;
  width: 100%;
  gap: 8px;
  background-color: var(--code-viewer-bg);
}

.file-explorer {
  display: flex;
  // width: 20%;
  height: 80vh;
  padding: 10px 19px; /* Adjusted padding */
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  border-radius: 0px 0px 12px 12px;
  border: 1px solid var(--code-viewer-border);
  background: transparent; /* Make the background transparent */
  overflow-y: auto; /* Added overflow-y to handle vertical overflow */
}

.search-container {
  width: 100%;
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid var(--code-viewer-search-border);
  background: var(--code-viewer-search-bg);
  color: var(--code-viewer-text);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;

  &:focus {
    border-color: var(--code-viewer-search-focus);
  }

  &::placeholder {
    color: var(--code-viewer-search-placeholder);
  }
}

.file-item {
  width: 100%;
  font-size: 14px;
  display: flex;
  flex-direction: column;
}

.file-row {
  padding: 2px 0;
  transition: background-color 0.2s;

  &.light-theme {
    color: var(--code-viewer-tree-text);

    .folder-text,
    .file-text {
      color: var(--code-viewer-tree-text);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .arrow-icon {
      color: var(--code-viewer-arrow);
    }
  }

  &.dark-theme {
    color: var(--code-viewer-text);

    .folder-text,
    .file-text {
      color: var(--code-viewer-text);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .arrow-icon {
      color: var(--code-viewer-arrow);
    }
  }
}

.folder-item {
  display: flex;
  width: 100%;
  cursor: pointer;
  user-select: none;

  .file-box {
    &:hover {
      background-color: var(--code-viewer-folder-hover);
    }
  }
}

.file-item-name {
  display: block;
  width: 100%;
  cursor: pointer;
}

.toggle-icon {
  width: 16px;
  display: inline-block;
  text-align: center;
}

.nested-files {
  width: 100%;
}

.editor-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0px 0px 12px 12px;
  border: 1px solid var(--code-viewer-border);
  background: transparent; /* Make background transparent */
  position: relative;
}

.monaco-editor-container {
  flex: 1;
  overflow: hidden;
  padding: 10px 19px; /* Adjusted padding */
  background: transparent; /* Make background transparent */
  height: 80vh;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 8px;
}

// ENHANCED: Center-aligned welcome message with proper responsive design and theme support
.welcome-message {
  position: absolute;
  top: 32%;
  // left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  // max-width: 90%;
  width: 100%;
  z-index: 10;

  // Ensure proper spacing and layout
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  // Responsive design
  @media (max-width: 768px) {
    max-width: 95%;
    padding: 0 1rem;
  }

  @media (max-width: 480px) {
    max-width: 98%;
    padding: 0 0.5rem;
  }
}

/* Make the Monaco Editor background transparent */
.monaco-editor .monaco-editor-background,
.monaco-editor .margin,
.monaco-editor .lines-content,
.monaco-editor .view-lines,
.monaco-editor .cursor,
.monaco-editor .line-numbers,
.monaco-editor .view-overlays,
.monaco-editor .contentWidgets,
.monaco-editor .decorationsOverviewRuler,
.monaco-editor .overview-ruler,
.monaco-editor .scrollbar,
.monaco-editor .scrollbar-vertical,
.monaco-editor .scrollbar-horizontal,
.monaco-editor .scrollbar-slider {
  background-color: transparent !important;
}

/* Ensure decorationsOverviewRuler is fully transparent */
.monaco-editor .decorationsOverviewRuler,
.monaco-editor .overview-ruler,
.monaco-editor .decorationsOverviewRuler *,
.monaco-editor .overview-ruler * {
  background-color: #00000000 !important;
  border-color: #00000000 !important;
}

/* Additional selectors to ensure transparency */
.monaco-editor,
.monaco-editor-background,
.monaco-editor .margin-view-overlays,
.monaco-editor .margin-view-overlays .line-numbers,
.monaco-editor .margin-view-overlays .line-numbers .relative-current-line-number,
.monaco-editor .inputarea,
.monaco-editor .view-overlays,
.monaco-editor .view-overlays .current-line,
.monaco-editor .view-overlays .current-line-margin {
  background-color: transparent !important;
}

/* Make the minimap transparent */
.monaco-editor .minimap,
.monaco-editor .minimap-slider,
.monaco-editor .minimap-decorations-layer,
.monaco-editor .minimap-shadow-hidden {
  background-color: transparent !important;
}

/* Style the minimap slider */
.monaco-editor .minimap-slider {
  background-color: rgba(100, 100, 100, 0.2) !important;
}

.monaco-editor .minimap-slider:hover {
  background-color: rgba(100, 100, 100, 0.35) !important;
}

/* Theme-specific Monaco editor adjustments */
.light-theme {
  /* Light theme specific Monaco editor styles */
  .monaco-editor {
    .minimap-slider {
      background-color: rgba(0, 0, 0, 0.15) !important;

      &:hover {
        background-color: rgba(0, 0, 0, 0.25) !important;
      }
    }

    .current-line {
      background-color: rgba(0, 0, 0, 0.04) !important; /* Very light black for line highlight */
    }

    .line-numbers {
      color: rgba(0, 0, 0, 0.5) !important; /* Semi-transparent black for line numbers */
    }

    .decorationsOverviewRuler,
    .overview-ruler {
      background-color: #00000000 !important;

      * {
        background-color: #00000000 !important;
      }
    }
  }
}

.dark-theme {
  /* Dark theme specific Monaco editor styles */
  .monaco-editor {
    .minimap-slider {
      background-color: rgba(255, 255, 255, 0.15) !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.25) !important;
      }
    }

    .current-line {
      background-color: rgba(255, 255, 255, 0.05) !important; /* Very light white for line highlight */
    }

    .line-numbers {
      color: rgba(255, 255, 255, 0.5) !important; /* Semi-transparent white for line numbers */
    }

    .decorationsOverviewRuler,
    .overview-ruler {
      background-color: #00000000 !important;

      * {
        background-color: #00000000 !important;
      }
    }
  }
}

.file-row.light-theme {
  color: var(--Text-Black, #000);
  font-family: Mulish, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.file-row.dark-theme {
  color: var(--Text-White, #fff);
  font-family: Mulish, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.file-box {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 4px;
  transition: all 0.3s ease;
  border-radius: 4px;
  position: relative;

  &:hover {
    background-color: var(--code-viewer-file-hover);
  }
}

.file-icon-wrapper {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  padding-right: 8px;

  img {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }
}

.arrow-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  font-size: 10px;
  transition: transform 0.2s ease;
  cursor: pointer;
  color: var(--code-viewer-arrow);
  flex-shrink: 0;

  &.expanded {
    transform: rotate(90deg);
  }
}

.light-theme {
  .arrow-icon {
    color: #000;
  }

  .folder-item:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  // Light theme specific selected file styles
  .file-box.selected {
    background-color: rgba(101, 102, 205, 0.1);
    border-left: 2px solid #6566CD;
    padding-left: 4px;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 4px;
      box-shadow: 0 0 4px rgba(101, 102, 205, 0.3);
      pointer-events: none;
    }

    .file-text {
      color: #6566CD;
      font-weight: 500;
    }
  }

  .search-input {
    background: rgba(255, 255, 255, 0.7); /* Semi-transparent white background */
    color: #000;
    border-color: #ddd;

    &:focus {
      border-color: #666;
    }

    &::placeholder {
      color: #999;
    }
  }
}

.dark-theme {
  .arrow-icon {
    color: #fff;
  }

  .folder-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  // Dark theme specific selected file styles
  .file-box.selected {
    background-color: rgba(255, 64, 129, 0.15);
    border-left: 2px solid #FF4081;
    padding-left: 4px;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 4px;
      box-shadow: 0 0 6px rgba(255, 64, 129, 0.4);
      pointer-events: none;
    }

    .file-text {
      color: #FF4081;
      font-weight: 500;
    }
  }

  .search-input {
    background: rgba(20, 22, 31, 0.7); /* Semi-transparent dark background */
    color: #fff;
    border-color: #292c3d;

    &:focus {
      border-color: #4b4e61;
    }

    &::placeholder {
      color: #666;
    }
  }
}

.tab-header {
  border-bottom: 1px solid var(--code-viewer-border);
  background: transparent; /* Make background transparent */
  height: 36px;
  overflow: hidden;
}

.tab-list {
  display: flex;
  display: flex;
  align-items: center;
  height: 100%;
  width: max-content;
  min-width: 100%;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--code-viewer-tab-scroll) transparent;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--code-viewer-tab-scroll);
    border-radius: 2px;
  }
}

.tab-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  max-width: 200px;
  height: 100%;
  padding: 0 12px;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  border-right: 1px solid var(--code-viewer-border);
  background: transparent; /* Make background transparent */
  color: var(--code-viewer-tab-text);
  cursor: pointer;
  transition: background-color 0.2s;

  &.active {
    background: rgba(255, 255, 255, 0.1); /* Semi-transparent background */
    color: var(--code-viewer-tab-active-text);
    border-bottom: 2px solid var(--primary-start, #6566CD); /* Use primary color for active tab */
  }

  &:hover:not(.active) {
    &:hover:not(.active) {
      background: rgba(255, 255, 255, 0.05); /* Semi-transparent hover background */
    }

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 8px;
    }
  }

  .close-button {
    opacity: 0;
    width: 16px;
    height: 16px;
    padding: 2px;
    margin-left: 8px;
    border: none;
    background: transparent;
    color: var(--code-viewer-tab-text);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: opacity 0.2s ease;

    &:hover {
      background: var(--code-viewer-tab-close-hover);
    }
  }

  &:hover {
    .close-button {
      opacity: 1;
    }
  }

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }
}
::ng-deep .file-row.dark-theme{
  background: transparent !important;
}

.monaco-loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(2px);
  z-index: 10;

  .loading-content {
    text-align: center;
    padding: 2rem;
    background: var(--code-viewer-bg);
    border-radius: 12px;
    border: 1px solid var(--code-viewer-border);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    max-width: 400px;

    h3 {
      margin: 1rem 0 0.5rem 0;
      color: var(--code-viewer-text);
      font-size: 1.2rem;
      font-weight: 600;
    }

    p {
      margin: 0 0 1.5rem 0;
      color: var(--code-viewer-text-secondary);
      font-size: 0.9rem;
    }
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--code-viewer-border);
    border-top: 3px solid #6566CD;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
  }

  .loading-progress {
    width: 100%;
    height: 4px;
    background: var(--code-viewer-border);
    border-radius: 2px;
    overflow: hidden;

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #6566CD, #FF4081);
      border-radius: 2px;
      animation: progress 2s ease-in-out infinite;
    }
  }
}

.monaco-error-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 0, 0, 0.05);
  backdrop-filter: blur(2px);
  z-index: 10;

  .error-content {
    text-align: center;
    padding: 2rem;
    background: var(--code-viewer-bg);
    border-radius: 12px;
    border: 1px solid #ff4444;
    box-shadow: 0 8px 32px rgba(255, 68, 68, 0.1);
    max-width: 500px;

    .error-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    h3 {
      margin: 0 0 1rem 0;
      color: #ff4444;
      font-size: 1.2rem;
      font-weight: 600;
    }

    p {
      margin: 0 0 1.5rem 0;
      color: var(--code-viewer-text-secondary);
      font-size: 0.9rem;
      word-break: break-word;
    }

    .retry-button {
      padding: 0.75rem 1.5rem;
      background: #6566CD;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: #5555bb;
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }
}

// ENHANCED: Welcome content with improved styling and theme support
.welcome-message {
  .welcome-content {
    text-align: center;
    color: var(--code-viewer-text-secondary, #666);
    padding: 2rem;
    border-radius: 12px;
    background-color: var(--code-viewer-welcome-bg, rgba(255, 255, 255, 0.05));
    border: 1px solid var(--code-viewer-welcome-border, rgba(0, 0, 0, 0.1));
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    // Smooth transitions for theme changes
    transition: all 0.3s ease;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--code-viewer-text, #333);
      font-size: 1.25rem;
      font-weight: 600;
      letter-spacing: -0.025em;

      // Add subtle icon or emoji
      &::before {
        content: '📝';
        display: block;
        font-size: 2rem;
        margin-bottom: 0.5rem;
        opacity: 0.8;
      }
    }

    p {
      margin: 0;
      font-size: 1rem;
      line-height: 1.5;
      opacity: 0.8;
    }

    // Responsive adjustments
    @media (max-width: 768px) {
      padding: 1.5rem;

      h3 {
        font-size: 1.1rem;

        &::before {
          font-size: 1.5rem;
        }
      }

      p {
        font-size: 0.9rem;
      }
    }

    @media (max-width: 480px) {
      padding: 1rem;

      h3 {
        font-size: 1rem;

        &::before {
          font-size: 1.25rem;
        }
      }

      p {
        font-size: 0.85rem;
      }
    }
  }
}

// Dark theme support for welcome message
:host-context(.dark-theme) .welcome-message .welcome-content {
  background-color: var(--code-viewer-welcome-bg-dark, rgba(0, 0, 0, 0.3));
  border-color: var(--code-viewer-welcome-border-dark, rgba(255, 255, 255, 0.1));
  color: var(--code-viewer-text-secondary-dark, #a0a0a0);

  h3 {
    color: var(--code-viewer-text-dark, #e0e0e0);
  }
}

.monaco-editor-container.hidden {
  display: none;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progress {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(0%); }
  100% { transform: translateX(100%); }
}
